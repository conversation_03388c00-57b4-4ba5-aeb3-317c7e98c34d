import os
import json
import requests
from typing import Dict, Any, List, Optional
import braintrust
from pydantic import BaseModel, Field

# Initialize Braintrust project
project = braintrust.projects.create(name="gemini-search-tools")

class SearchParams(BaseModel):
    query: str = Field(description="The search query to execute")
    model: str = Field(default="gemini-1.5-pro", description="The Gemini model to use")

class SearchResult(BaseModel):
    response: str
    grounding_metadata: Optional[Dict[str, Any]] = None
    search_queries: List[str] = []
    sources: List[Dict[str, str]] = []

def gemini_search_handler(query: str, model: str = "gemini-1.5-pro") -> Dict[str, Any]:
    """
    Handler function that calls Gemini with googleSearch tool enabled
    """
    gemini_api_key = os.getenv("GEMINI_API_KEY")
    
    if not gemini_api_key:
        raise ValueError("GEMINI_API_KEY environment variable is required")
    
    url = f"https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent"
    
    headers = {
        "Content-Type": "application/json",
        "x-goog-api-key": gemini_api_key,
    }
    
    payload = {
        "contents": [
            {
                "role": "user",
                "parts": [{"text": query}],
            }
        ],
        "tools": [
            {
                "googleSearch": {}
            }
        ],
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()
        
        data = response.json()
        
        # Extract the response text and grounding metadata
        candidate = data.get("candidates", [{}])[0]
        response_text = candidate.get("content", {}).get("parts", [{}])[0].get("text", "")
        grounding_metadata = candidate.get("groundingMetadata")
        
        # Extract search queries and sources
        search_queries = grounding_metadata.get("webSearchQueries", []) if grounding_metadata else []
        sources = []
        
        if grounding_metadata and "groundingChunks" in grounding_metadata:
            sources = [
                {
                    "title": chunk.get("web", {}).get("title", ""),
                    "uri": chunk.get("web", {}).get("uri", ""),
                }
                for chunk in grounding_metadata["groundingChunks"]
            ]
        
        return {
            "response": response_text,
            "grounding_metadata": grounding_metadata,
            "search_queries": search_queries,
            "sources": sources,
        }
        
    except requests.exceptions.RequestException as e:
        raise Exception(f"Failed to call Gemini with search: {str(e)}")
    except Exception as e:
        raise Exception(f"Error processing Gemini response: {str(e)}")

# Create the Braintrust tool
gemini_search_tool = project.tools.create(
    name="Gemini Search",
    slug="gemini-search",
    description="Search the web using Gemini's built-in Google Search capability",
    handler=gemini_search_handler,
    parameters=SearchParams,
    if_exists="replace",
)

# Create a prompt that uses the search tool
search_prompt = project.prompts.create(
    name="Web Search Assistant",
    slug="web-search-assistant",
    description="An assistant that can search the web for current information",
    model="gpt-4o",  # Use any model through Braintrust proxy
    messages=[
        {
            "role": "system",
            "content": "You are a helpful assistant that can search the web for current information. When a user asks a question that requires up-to-date information, use the Gemini Search tool to find relevant information and provide a comprehensive answer with sources.",
        },
        {
            "role": "user",
            "content": "{{question}}",
        },
    ],
    tools=[gemini_search_tool],
    if_exists="replace",
)

if __name__ == "__main__":
    # Example usage
    print("Testing Gemini Search Tool...")
    
    # Test the tool directly
    try:
        result = gemini_search_handler("What's the latest news about AI in 2024?")
        print("Search Result:")
        print(f"Response: {result['response']}")
        print(f"Search Queries: {result['search_queries']}")
        print(f"Sources: {len(result['sources'])} sources found")
        for i, source in enumerate(result['sources'][:3]):  # Show first 3 sources
            print(f"  {i+1}. {source['title']} - {source['uri']}")
    except Exception as e:
        print(f"Error: {e}")
