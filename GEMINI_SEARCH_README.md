# Gemini Search Tool for Braintrust

This project creates a Braintrust wrapper tool that enables you to use <PERSON>'s built-in `googleSearch` capability through Braintrust's tool system.

## Setup

### 1. Install Dependencies

```bash
# For TypeScript/JavaScript
npm install braintrust zod

# For Python
pip install braintrust pydantic requests
```

### 2. Get API Keys

1. **Gemini API Key**: Get from [Google AI Studio](https://aistudio.google.com/app/apikey)
2. **Braintrust API Key**: Get from [Braintrust Settings](https://www.braintrust.dev/app/settings)

### 3. Set Environment Variables

```bash
export GEMINI_API_KEY="your-gemini-api-key"
export BRAINTRUST_API_KEY="your-braintrust-api-key"
```

## Usage

### Option 1: TypeScript/JavaScript

```typescript
import braintrust from "braintrust";

// Use the tool directly
const result = await braintrust.invoke({
  projectName: "gemini-search-tools",
  slug: "gemini-search",
  input: {
    query: "What's the latest news about AI?",
    model: "gemini-1.5-pro" // optional
  }
});

console.log(result.response);
console.log(result.sources);
```

### Option 2: Python

```python
import braintrust

# Use the tool directly
result = braintrust.invoke(
    project_name="gemini-search-tools",
    slug="gemini-search",
    input={
        "query": "What's the latest news about AI?",
        "model": "gemini-1.5-pro"  # optional
    }
)

print(result["response"])
print(result["sources"])
```

### Option 3: Braintrust Playground

1. Run the setup script to create the tools and prompts
2. Go to [Braintrust Dashboard](https://www.braintrust.dev/app)
3. Navigate to the "gemini-search-tools" project
4. Open the Playground
5. Select the "Web Search Assistant" prompt
6. Ask questions that require current information

## How It Works

### The Wrapper Tool

The wrapper tool:
1. Takes a search query as input
2. Calls Gemini's API with the `googleSearch` tool enabled
3. Extracts the response, search queries used, and sources
4. Returns structured data that can be used by other models

### Response Format

```json
{
  "response": "The AI response with current information",
  "grounding_metadata": { /* Full Gemini grounding metadata */ },
  "search_queries": ["query1", "query2"],
  "sources": [
    {
      "title": "Source Title",
      "uri": "https://example.com"
    }
  ]
}
```

### Integration with Other Models

The beauty of this approach is that you can:
1. Use Gemini's search capability to get current information
2. Feed that information to any other model (GPT-4, Claude, etc.) through Braintrust
3. Get the best of both worlds: Gemini's search + your preferred model's reasoning

## Example Use Cases

1. **Current Events**: "What's happening in the 2024 Olympics?"
2. **Market Data**: "What are the current stock prices for FAANG companies?"
3. **Recent Developments**: "What are the latest breakthroughs in quantum computing?"
4. **Weather & Local Info**: "What's the weather in Tokyo and what events are happening there?"

## Advanced Features

### Custom Model Selection

```typescript
const result = await braintrust.invoke({
  projectName: "gemini-search-tools",
  slug: "gemini-search",
  input: {
    query: "Your search query",
    model: "gemini-2.0-flash" // Use different Gemini models
  }
});
```

### Batch Processing

```typescript
const queries = [
  "Latest AI news",
  "Current crypto prices",
  "Space exploration updates"
];

const results = await Promise.all(
  queries.map(query => 
    braintrust.invoke({
      projectName: "gemini-search-tools",
      slug: "gemini-search",
      input: { query }
    })
  )
);
```

## Troubleshooting

### Common Issues

1. **"GEMINI_API_KEY environment variable is required"**
   - Make sure you've set the `GEMINI_API_KEY` environment variable
   - Verify the API key is valid

2. **"Gemini API error: 403"**
   - Check that your Gemini API key has the necessary permissions
   - Ensure you're using a supported model

3. **Tool not found in playground**
   - Run the setup script to create the tools
   - Refresh the Braintrust dashboard

### Supported Models

- `gemini-1.5-pro`
- `gemini-1.5-flash`
- `gemini-2.0-flash`
- Other Gemini models that support the `googleSearch` tool

## Next Steps

1. Customize the tool for your specific use cases
2. Add error handling and retry logic
3. Implement caching for frequently searched queries
4. Create specialized prompts for different domains (news, finance, etc.)
