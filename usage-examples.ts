import braintrust from "braintrust";
import { geminiSearchTool, searchPrompt } from "./gemini-search-tool";

// Example 1: Using the tool directly
async function directToolUsage() {
  console.log("=== Direct Tool Usage ===");
  
  try {
    const result = await braintrust.invoke({
      projectName: "gemini-search-tools",
      slug: "gemini-search",
      input: {
        query: "What are the latest developments in AI in 2024?",
        model: "gemini-1.5-pro"
      }
    });
    
    console.log("Search Response:", result.response);
    console.log("Search Queries Used:", result.search_queries);
    console.log("Sources Found:", result.sources.length);
    
    // Display sources
    result.sources.forEach((source: any, index: number) => {
      console.log(`${index + 1}. ${source.title} - ${source.uri}`);
    });
    
  } catch (error) {
    console.error("Error:", error);
  }
}

// Example 2: Using the prompt with the tool in the playground
async function promptUsage() {
  console.log("\n=== Prompt Usage ===");
  
  try {
    const result = await braintrust.invoke({
      projectName: "gemini-search-tools",
      slug: "web-search-assistant",
      input: {
        question: "What's the current weather in Tokyo and what major events are happening there this week?"
      }
    });
    
    console.log("Assistant Response:", result);
    
  } catch (error) {
    console.error("Error:", error);
  }
}

// Example 3: Using in the Braintrust playground
function playgroundInstructions() {
  console.log("\n=== Playground Usage Instructions ===");
  console.log("1. Go to your Braintrust dashboard");
  console.log("2. Navigate to the 'gemini-search-tools' project");
  console.log("3. Open the Playground");
  console.log("4. Select the 'Web Search Assistant' prompt");
  console.log("5. Enter a question that requires current information");
  console.log("6. The assistant will automatically use the Gemini Search tool when needed");
  console.log("\nExample questions to try:");
  console.log("- What's the latest news about OpenAI?");
  console.log("- What are the current stock prices for major tech companies?");
  console.log("- What's happening in the 2024 Olympics?");
  console.log("- What are the latest developments in quantum computing?");
}

// Example 4: Advanced usage with custom processing
async function advancedUsage() {
  console.log("\n=== Advanced Usage with Custom Processing ===");
  
  const queries = [
    "Latest AI breakthroughs in 2024",
    "Current cryptocurrency market trends",
    "Recent space exploration missions"
  ];
  
  for (const query of queries) {
    try {
      console.log(`\nSearching: ${query}`);
      
      const result = await braintrust.invoke({
        projectName: "gemini-search-tools",
        slug: "gemini-search",
        input: { query }
      });
      
      // Process and summarize results
      const summary = {
        query,
        responseLength: result.response.length,
        searchQueriesUsed: result.search_queries.length,
        sourcesFound: result.sources.length,
        topSources: result.sources.slice(0, 3).map((s: any) => s.title)
      };
      
      console.log("Summary:", JSON.stringify(summary, null, 2));
      
    } catch (error) {
      console.error(`Error searching "${query}":`, error);
    }
  }
}

// Run examples
async function main() {
  // Make sure you have GEMINI_API_KEY set in your environment
  if (!process.env.GEMINI_API_KEY) {
    console.error("Please set GEMINI_API_KEY environment variable");
    process.exit(1);
  }
  
  await directToolUsage();
  await promptUsage();
  playgroundInstructions();
  await advancedUsage();
}

// Uncomment to run examples
// main().catch(console.error);

export { directToolUsage, promptUsage, advancedUsage };
