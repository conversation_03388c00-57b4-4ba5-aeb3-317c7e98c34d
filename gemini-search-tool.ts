import braintrust from "braintrust";
import { z } from "zod";

const project = braintrust.projects.create({ name: "gemini-search-tools" });

// Create a wrapper tool that calls <PERSON> with googleSearch enabled
export const geminiSearchTool = project.tools.create({
  name: "Gemini Search",
  slug: "gemini-search",
  description: "Search the web using Gemini's built-in Google Search capability",
  parameters: z.object({
    query: z.string().describe("The search query to execute"),
    model: z.string().optional().default("gemini-1.5-pro").describe("The Gemini model to use"),
  }),
  handler: async ({ query, model = "gemini-1.5-pro" }) => {
    const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
    
    if (!GEMINI_API_KEY) {
      throw new Error("GEMINI_API_KEY environment variable is required");
    }

    try {
      const response = await fetch(
        `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "x-goog-api-key": GEMINI_API_KEY,
          },
          body: JSON.stringify({
            contents: [
              {
                role: "user",
                parts: [{ text: query }],
              },
            ],
            tools: [
              {
                googleSearch: {},
              },
            ],
          }),
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Gemini API error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      
      // Extract the response text and grounding metadata
      const candidate = data.candidates?.[0];
      const responseText = candidate?.content?.parts?.[0]?.text || "";
      const groundingMetadata = candidate?.groundingMetadata;

      return {
        response: responseText,
        groundingMetadata: groundingMetadata || null,
        searchQueries: groundingMetadata?.webSearchQueries || [],
        sources: groundingMetadata?.groundingChunks?.map((chunk: any) => ({
          title: chunk.web?.title,
          uri: chunk.web?.uri,
        })) || [],
      };
    } catch (error) {
      throw new Error(`Failed to call Gemini with search: ${error instanceof Error ? error.message : String(error)}`);
    }
  },
  ifExists: "replace",
});

// Create a prompt that uses the search tool
export const searchPrompt = project.prompts.create({
  name: "Web Search Assistant",
  slug: "web-search-assistant",
  description: "An assistant that can search the web for current information",
  model: "gpt-4o", // Use any model through Braintrust proxy
  messages: [
    {
      role: "system",
      content: "You are a helpful assistant that can search the web for current information. When a user asks a question that requires up-to-date information, use the Gemini Search tool to find relevant information and provide a comprehensive answer with sources.",
    },
    {
      role: "user",
      content: "{{question}}",
    },
  ],
  tools: [geminiSearchTool],
  ifExists: "replace",
});
